{"compilerOptions": {"target": "esnext", "lib": ["es2017", "es2018", "es5", "dom"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/**/*.ts", "src/**/*.tsx", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios", "packages/**/*", "mobile/**/*"], "references": [{"path": "../../packages/shared"}, {"path": "../../packages/ui"}, {"path": "../../packages/hooks"}, {"path": "../../packages/services"}, {"path": "../../packages/utils"}, {"path": "../../packages/config"}]}