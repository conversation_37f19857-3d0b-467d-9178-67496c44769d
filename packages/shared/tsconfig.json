{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "composite": true, "tsBuildInfoFile": "./dist/tsconfig.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}