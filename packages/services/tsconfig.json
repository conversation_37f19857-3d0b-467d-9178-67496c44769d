{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "composite": true}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/services/**/*", "src/services/**/*.ts", "src/services/**/*.tsx"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}, {"path": "../utils"}]}