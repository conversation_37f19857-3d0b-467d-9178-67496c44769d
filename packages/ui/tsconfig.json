{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "jsx": "react-native", "resolveJsonModule": true, "composite": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}, {"path": "../hooks"}, {"path": "../services"}, {"path": "../utils"}]}