{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020", "DOM", "DOM.Iterable"], "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "jsx": "react", "resolveJsonModule": true, "composite": true}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/hooks/**/*", "src/hooks/**/*.ts", "src/contexts/**/*", "src/contexts/**/*.tsx"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}, {"path": "../services"}]}